#!/bin/bash

# Compilation script for Myers diff C++ library with comprehensive Diff class

echo "Compiling Myers diff C++ library with complete Diff class implementation..."

# Parse command line arguments
ENABLE_OPENMP=false
BUILD_TYPE="standard"

while [[ $# -gt 0 ]]; do
    case $1 in
        --openmp)
            ENABLE_OPENMP=true
            BUILD_TYPE="openmp"
            shift
            ;;
        --help)
            echo "Usage: $0 [--openmp] [--help]"
            echo "  --openmp    Enable OpenMP parallelization (requires OpenMP runtime)"
            echo "  --help      Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

if [[ "$OSTYPE" == "linux-gnu"* ]] || [[ "$OSTYPE" == "darwin"* ]]; then
    # Linux or macOS
    echo "Compiling for Linux/macOS..."

    if [ "$ENABLE_OPENMP" = true ]; then
        echo "Building with OpenMP support..."
        # Use C++17 standard for template features with OpenMP support
        g++ -shared -fPIC -O3 -march=native  -fopenmp -std=c++17 -fopenmp -DENABLE_OPENMP -o myers_diff.so myers_diff.cpp
        LIBRARY_NAME="myers_diff_openmp.so"
    else
        echo "Building without OpenMP (standard build)..."
        # Use C++17 standard for template features without OpenMP
        g++ -shared -fPIC -O3 -march=native -std=c++17 -o myers_diff.so myers_diff.cpp
        LIBRARY_NAME="myers_diff.so"
    fi

    if [ $? -eq 0 ]; then
        echo "Successfully compiled $LIBRARY_NAME with complete Diff class"

        # Optional: Create a simple test to verify the library loads
        echo "Verifying library can be loaded..."
        if command -v nm &> /dev/null; then
            echo "Exported symbols:"
            nm -D "$LIBRARY_NAME" | grep myers_ | head -5
        fi

        echo "Library size: $(ls -lh "$LIBRARY_NAME" | awk '{print $5}')"

        if [ "$ENABLE_OPENMP" = true ]; then
            echo "OpenMP build completed. Note: This library requires OpenMP runtime to be available."
            echo "If you encounter loading issues in Python, use the standard build instead."
        fi
    else
        echo "Compilation failed"
        exit 1
    fi
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "win32" ]]; then
    # Windows
    echo "Compiling for Windows..."
    echo "Please use Visual Studio x64 Native Tools Command Prompt and run:"

    if [ "$ENABLE_OPENMP" = true ]; then
        echo "For OpenMP build:"
        echo "cl /LD /O2 /std:c++17 /openmp /DENABLE_OPENMP /DMYERS_DIFF_EXPORTS myers_diff.cpp /Fe:myers_diff_openmp.dll /link /MACHINE:X64"
        echo ""
        echo "Alternative command for 32-bit:"
        echo "cl /LD /O2 /std:c++17 /openmp /DENABLE_OPENMP /DMYERS_DIFF_EXPORTS myers_diff.cpp /Fe:myers_diff_openmp.dll"
    else
        echo "For standard build (no OpenMP):"
        echo "cl /LD /O2 /std:c++17 /DMYERS_DIFF_EXPORTS myers_diff.cpp /Fe:myers_diff.dll /link /MACHINE:X64"
        echo ""
        echo "Alternative command for 32-bit:"
        echo "cl /LD /O2 /std:c++17 /DMYERS_DIFF_EXPORTS myers_diff.cpp /Fe:myers_diff.dll"
    fi
    echo ""
    echo "Note: The implementation requires C++17 for template features"
else
    echo "Unknown OS type: $OSTYPE"
    exit 1
fi

echo "Compilation complete!"
echo ""
echo "The library now includes:"
echo "- Complete template-based Diff class"
echo "- Zero-copy algorithms using iterators"
echo "- Binary search-based prefix/suffix detection"
echo "- Full C API compatibility with new myers_diff_compute() function"
echo "- Explicit template instantiation for int type"

if [ "$ENABLE_OPENMP" = true ]; then
    echo "- OpenMP parallelization for improved performance (enabled)"
    echo ""
    echo "IMPORTANT: This OpenMP build requires OpenMP runtime libraries."
    echo "If Python fails to load the library, compile without --openmp flag."
else
    echo "- Optional OpenMP parallelization (disabled in this build)"
    echo ""
    echo "To enable OpenMP parallelization, recompile with --openmp flag."
fi
