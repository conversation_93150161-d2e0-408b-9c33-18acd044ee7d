#!/bin/bash

# <PERSON>ript to build both standard and OpenMP versions of the Myers diff library

echo "Building all versions of Myers diff library..."
echo "=============================================="

# Build standard version (no OpenMP)
echo ""
echo "1. Building standard version (no OpenMP dependencies)..."
./compile_myers_diff.sh

if [ $? -eq 0 ]; then
    echo "✅ Standard version built successfully"
else
    echo "❌ Standard version build failed"
    exit 1
fi

# Build OpenMP version
echo ""
echo "2. Building OpenMP version (requires OpenMP runtime)..."
./compile_myers_diff.sh --openmp

if [ $? -eq 0 ]; then
    echo "✅ OpenMP version built successfully"
else
    echo "❌ OpenMP version build failed"
    echo "Note: This is expected if OpenMP is not available on your system"
fi

echo ""
echo "Build summary:"
echo "=============="

if [ -f "myers_diff.so" ] || [ -f "myers_diff.dll" ]; then
    echo "✅ Standard version: Available (recommended for Python integration)"
fi

if [ -f "myers_diff_openmp.so" ] || [ -f "myers_diff_openmp.dll" ]; then
    echo "✅ OpenMP version: Available (use only if OpenMP runtime is available)"
fi

echo ""
echo "Usage recommendations:"
echo "- Use the standard version (myers_diff.so/dll) for Python integration"
echo "- Use the OpenMP version only if you have OpenMP runtime libraries installed"
echo "- If Python fails to load the OpenMP version, use the standard version instead"
